# Interactive Terminal Testing Guide

This guide explains how to use the interactive terminal testing system for the arcade middleware. This system allows you to manually control the entire 9-phase arcade workflow through terminal commands while maintaining all the real processing logic.

## Quick Start

### 1. Start Interactive Mode
```bash
# Set environment variable and start the application
MODE=INTERACTIVE npm run start:dev
```

### 2. Basic Commands
Once the application starts, you'll see an interactive terminal with available commands:

```
🎮 arcade-test> help
```

## Available Commands

### Core Workflow Commands

| Command | Description | Usage Example |
|---------|-------------|---------------|
| `badge <badge_id>` | Simulate badge scan with specified ID | `badge TEAM001` |
| `timer` | Simulate room timer expiration | `timer` |
| `complete` | Simulate all games completed | `complete` |
| `scores` | Input game scores manually | `scores` |

### Utility Commands

| Command | Description |
|---------|-------------|
| `help` | Show available commands |
| `status` | Show current workflow status |
| `reset` | Reset workflow to beginning |
| `clear` | Clear terminal screen |
| `exit` | Exit the application |

## 9-Phase Workflow Testing

### Phase 1: Badge Scanning
**What it does:** Waits for team member NFC badge scan
**How to test:**
```bash
🎮 arcade-test> badge TEAM001
```
**Expected output:**
- Badge scan confirmation
- LED color change to yellow
- Progression to Phase 2

### Phase 2: Team Authorization
**What it does:** Validates team access with backend API
**How to test:**
- Automatically triggered after badge scan
- Uses real API call to backend server
**Expected output:**
- Authorization success/failure message
- Progression to Phase 3 (if successful)

### Phase 3: Room Access Control
**What it does:** Controls lighting, access latches, and displays instructions
**How to test:**
- Automatically triggered after successful authorization
**Expected output:**
- Room lighting turned on
- Access latch opened
- Game instructions displayed
- Progression to Phase 4

### Phase 4: Arcade Game Session Management
**What it does:** Starts arcade machines and monitors timers
**How to test:**
- Automatically triggered after room setup
**Expected output:**
- Arcade machines started
- Room timer activated
- Progression to Phase 5

### Phase 5: Score Collection
**What it does:** Receives final scores from arcade machines
**How to test:**
```bash
# Option 1: Simulate timer expiration
🎮 arcade-test> timer

# Option 2: Simulate games completion
🎮 arcade-test> complete

# Then input scores
🎮 arcade-test> scores
```
**Expected output:**
- Session end trigger (timer or completion)
- Score input prompts
- Progression to Phase 6

### Phase 6: Result Evaluation
**What it does:** Determines win/loss/jackpot status based on scores
**How to test:**
- Automatically triggered after score input
**Expected output:**
- Score evaluation
- Win/loss/jackpot determination
- Progression to Phase 7

### Phase 7: Backend Score Submission
**What it does:** Submits team results to game management system
**How to test:**
- Automatically triggered after result evaluation
- Uses real API call to backend server
**Expected output:**
- Score submission confirmation
- Progression to Phase 8

### Phase 8: End Game Effects
**What it does:** Displays appropriate animations (win/loss/jackpot)
**How to test:**
- Automatically triggered after backend submission
**Expected output:**
- Win/loss/jackpot animation
- Celebration effects (if applicable)
- Progression to Phase 9

### Phase 9: Session Cleanup
**What it does:** Resets system for next team
**How to test:**
- Automatically triggered after end game effects
**Expected output:**
- System reset
- Return to Phase 1 (waiting for next badge)

## Test Badge IDs

The system includes several predefined test badge IDs:

- `TEAM001` - Standard team badge
- `TEAM002` - Standard team badge
- `TEAM003` - Standard team badge
- `ADMIN001` - Admin badge
- `TEST_BADGE` - Test badge
- `DEMO_TEAM` - Demo team badge
- `VIP_001` - VIP badge
- `GUEST_123` - Guest badge

## Score Testing Scenarios

### Winning Scenario
```bash
🎮 arcade-test> scores
Game 1 score: 800
Game 2 score: 750
Game 3 score: 900
Game 4 score: 850
```

### Losing Scenario (All Zeros)
```bash
🎮 arcade-test> scores
Game 1 score: 0
Game 2 score: 0
Game 3 score: 0
Game 4 score: 0
```

### Jackpot Scenario
```bash
🎮 arcade-test> scores
Game 1 score: 5000
Game 2 score: 800
Game 3 score: 750
Game 4 score: 900
```

## Monitoring and Debugging

### Check Current Status
```bash
🎮 arcade-test> status
```
This shows:
- Current workflow phase
- Phase completion status
- Available next actions

### Reset Workflow
If you need to restart the workflow:
```bash
🎮 arcade-test> reset
```

### Clear Screen
To clean up the terminal:
```bash
🎮 arcade-test> clear
```

## Integration with Real Hardware

The interactive testing system maintains all the real processing logic:

- **API Calls:** Real calls to backend server at `https://vmi1015553.contaboserver.net:9010/`
- **Score Evaluation:** Real jackpot threshold checking
- **Session Management:** Real timer and game completion logic
- **Error Handling:** Real error handling and recovery

Only the hardware interfaces (badge reader, serial communication) are simulated through terminal input.

## Tips for Effective Testing

1. **Follow the Natural Flow:** Test the phases in order to simulate real usage
2. **Test Error Scenarios:** Try invalid badge IDs, authorization failures
3. **Test Different Score Combinations:** Verify win/loss/jackpot logic
4. **Monitor Logs:** Watch the console output for detailed processing information
5. **Use Status Command:** Regularly check `status` to understand current state

## Troubleshooting

### Common Issues

**Badge scan not working:**
- Ensure badge ID is alphanumeric, 4-20 characters
- Check for duplicate scans (5-second cooldown)

**Workflow stuck:**
- Use `status` to check current phase
- Use `reset` to restart workflow

**Commands not responding:**
- Check for typos in command names
- Use `help` to see available commands

**Application not starting:**
- Ensure `MODE=INTERACTIVE` environment variable is set
- Check for any dependency issues

### Getting Help

If you encounter issues:
1. Check the console logs for error messages
2. Use the `status` command to understand current state
3. Try `reset` to restart the workflow
4. Restart the application if needed

## Example Complete Test Session

```bash
# Start the application
MODE=INTERACTIVE npm run start:dev

# Check initial status
🎮 arcade-test> status

# Simulate badge scan
🎮 arcade-test> badge TEAM001

# Wait for authorization and room setup (automatic)

# Simulate game completion
🎮 arcade-test> complete

# Input scores
🎮 arcade-test> scores
Game 1 score: 800
Game 2 score: 750
Game 3 score: 900
Game 4 score: 850

# Watch the rest of the workflow complete automatically

# Check final status
🎮 arcade-test> status

# Reset for next test
🎮 arcade-test> reset
```

This interactive testing system provides a complete environment for testing the arcade middleware without requiring physical hardware, while maintaining all the real business logic and API integrations.
