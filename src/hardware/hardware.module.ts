import { DynamicModule, Module, Provider } from '@nestjs/common';
import {
  NFC_READER,
  SENSOR_BUS,
  DISPLAY,
  LED_CONTROL,
  SERIAL_CONTROL,
} from './tokens';
import { MockNfcReaderService } from '../simulation/mocks/mock-nfc-reader.service';
import { MockSensorBusService } from '../simulation/mocks/mock-sensor-bus.service';
import { MockLedService } from '../simulation/mocks/mock-led.service';
import { MockSerialService } from '../simulation/mocks/mock-serial.service';
import { InteractiveNfcReaderService } from '../simulation/mocks/interactive-nfc-reader.service';
import { InteractiveSerialService } from '../simulation/mocks/interactive-serial.service';
import { PcscLiteReaderService } from './nfc/nfc-pcsc-reader.service';
import { Rs232ReaderService } from './nfc/rs232-reader.service';
import { ControllinoSensorService } from './sensors/controllino-sensor.service';
import { ControllinoLedService } from './leds/controllino-led.service';
import { ControllinoSerialService } from './serial/controllino-serial.service';
import { ConsoleDisplayService } from '../simulation/mocks/console-display.service';
import { ConfigService } from '@nestjs/config';

@Module({})
export class HardwareModule {
  static register(mode: 'SIM' | 'PROD' | 'INTERACTIVE'): DynamicModule {
    const providers: Provider[] = [
      {
        provide: NFC_READER,
        useFactory: (cfg: ConfigService, terminalInterface?: any) => {
          if (mode === 'INTERACTIVE') {
            return new InteractiveNfcReaderService(terminalInterface);
          } else if (mode === 'SIM') {
            return new MockNfcReaderService();
          }

          // Choose reader type based on config
          const readerType = cfg.get<string>(
            'global.hardware.nfcReaderType',
            'PCSC',
          );
          if (readerType === 'RS232') {
            return new Rs232ReaderService(cfg);
          } else {
            return new PcscLiteReaderService();
          }
        },
        inject: [
          ConfigService,
          { token: 'TerminalInterfaceService', optional: true },
        ],
      },

      mode === 'PROD'
        ? { provide: SENSOR_BUS, useClass: ControllinoSensorService }
        : { provide: SENSOR_BUS, useClass: MockSensorBusService },

      mode === 'PROD'
        ? { provide: LED_CONTROL, useClass: ControllinoLedService }
        : { provide: LED_CONTROL, useClass: MockLedService },

      {
        provide: SERIAL_CONTROL,
        useFactory: (cfg: ConfigService, terminalInterface?: any) => {
          if (mode === 'INTERACTIVE') {
            return new InteractiveSerialService(terminalInterface);
          } else if (mode === 'PROD') {
            return new ControllinoSerialService(cfg);
          } else {
            return new MockSerialService();
          }
        },
        inject: [
          ConfigService,
          { token: 'TerminalInterfaceService', optional: true },
        ],
      },

      { provide: DISPLAY, useClass: ConsoleDisplayService },
    ];

    return {
      module: HardwareModule,
      providers,
      exports: providers,
    };
  }
}
