import { DynamicModule, Module } from '@nestjs/common';
import { HardwareModule } from 'src/hardware/hardware.module';
import { TerminalInterfaceService } from './terminal-interface.service';

@Module({})
export class SimulationModule {
  static registerAsync(options): DynamicModule {
    const enabled = options.mode === 'SIM' || options.mode === 'INTERACTIVE';
    const providers = enabled
      ? [
          TerminalInterfaceService,
          {
            provide: 'TerminalInterfaceService',
            useExisting: TerminalInterfaceService,
          },
        ]
      : [];

    return {
      module: SimulationModule,
      imports: enabled ? [HardwareModule.register(options.mode || 'SIM')] : [],
      providers,
      exports: enabled
        ? [HardwareModule, TerminalInterfaceService, 'TerminalInterfaceService']
        : [],
    };
  }
}
